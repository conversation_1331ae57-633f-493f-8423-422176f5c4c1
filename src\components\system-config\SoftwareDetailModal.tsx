import React, { useState } from 'react';
import { 
  X, 
  Download, 
  HardDrive, 
  Wifi, 
  Bluetooth, 
  Calendar, 
  User, 
  FileText, 
  Hash,
  ToggleLeft,
  ToggleRight,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { Button } from '../ui/button';
import { 
  Software, 
  updateSoftwareStatus, 
  downloadSoftware, 
  formatFileSize, 
  formatDate 
} from '../../utils/api/softwareApi';

interface SoftwareDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  software: Software;
  onUpdate: () => void;
}

export function SoftwareDetailModal({ isOpen, onClose, software, onUpdate }: SoftwareDetailModalProps) {
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  if (!isOpen) return null;

  // 處理狀態切換
  const handleToggleStatus = async () => {
    try {
      setUpdating(true);
      setError(null);
      await updateSoftwareStatus(software._id, { isEnabled: !software.isEnabled });
      onUpdate();
    } catch (err) {
      setError(err instanceof Error ? err.message : '更新狀態失敗');
    } finally {
      setUpdating(false);
    }
  };

  // 處理下載
  const handleDownload = async (type: 'original' | 'pure') => {
    try {
      setError(null);
      await downloadSoftware(software._id, type);
    } catch (err) {
      setError(err instanceof Error ? err.message : '下載失敗');
    }
  };

  // 獲取設備類型圖標
  const getDeviceIcon = () => {
    switch (software.deviceType) {
      case 'gateway':
        return <HardDrive className="w-5 h-5 text-blue-600" />;
      case 'epd':
        return <HardDrive className="w-5 h-5 text-green-600" />;
      default:
        return <HardDrive className="w-5 h-5 text-gray-600" />;
    }
  };

  // 獲取功能類型圖標
  const getFunctionIcon = () => {
    switch (software.functionType) {
      case 'wifi':
        return <Wifi className="w-5 h-5 text-blue-500" />;
      case 'ble':
        return <Bluetooth className="w-5 h-5 text-blue-500" />;
      default:
        return <HardDrive className="w-5 h-5 text-gray-500" />;
    }
  };

  // 獲取狀態顏色和文字
  const getStatusInfo = () => {
    if (!software.isEnabled) {
      return { color: 'text-gray-500', text: '已禁用', bgColor: 'bg-gray-100' };
    }
    
    switch (software.status) {
      case 'active':
        return { color: 'text-green-600', text: '啟用中', bgColor: 'bg-green-100' };
      case 'deprecated':
        return { color: 'text-yellow-600', text: '已棄用', bgColor: 'bg-yellow-100' };
      default:
        return { color: 'text-gray-500', text: '未知', bgColor: 'bg-gray-100' };
    }
  };

  const statusInfo = getStatusInfo();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* 標題列 */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            {getDeviceIcon()}
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{software.name}</h2>
              <p className="text-sm text-gray-600">版本 {software.version}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6">
          {/* 錯誤提示 */}
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center">
              <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
              <span className="text-red-700">{error}</span>
              <button 
                onClick={() => setError(null)}
                className="ml-auto text-red-500 hover:text-red-700"
              >
                ×
              </button>
            </div>
          )}

          {/* 狀態和操作區域 */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${statusInfo.bgColor} ${statusInfo.color}`}>
                  {statusInfo.text}
                </span>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  {getFunctionIcon()}
                  <span className="uppercase">{software.functionType}</span>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  onClick={handleToggleStatus}
                  disabled={updating}
                  variant="outline"
                  size="sm"
                  className={software.isEnabled ? 'text-red-600 hover:text-red-700' : 'text-green-600 hover:text-green-700'}
                >
                  {updating ? (
                    <div className="animate-spin w-4 h-4 border-2 border-current border-t-transparent rounded-full mr-2" />
                  ) : software.isEnabled ? (
                    <ToggleRight className="w-4 h-4 mr-2" />
                  ) : (
                    <ToggleLeft className="w-4 h-4 mr-2" />
                  )}
                  {software.isEnabled ? '禁用' : '啟用'}
                </Button>
                <Button
                  onClick={() => handleDownload('original')}
                  variant="outline"
                  size="sm"
                >
                  <Download className="w-4 h-4 mr-2" />
                  下載原始檔案
                </Button>
                <Button
                  onClick={() => handleDownload('pure')}
                  variant="outline"
                  size="sm"
                >
                  <Download className="w-4 h-4 mr-2" />
                  下載純韌體
                </Button>
              </div>
            </div>
          </div>

          {/* 基本資訊 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">基本資訊</h3>
              
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <FileText className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">軟體名稱</p>
                    <p className="font-medium">{software.name}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Hash className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">版本</p>
                    <p className="font-medium">{software.version}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  {getDeviceIcon()}
                  <div>
                    <p className="text-sm text-gray-600">設備類型</p>
                    <p className="font-medium capitalize">{software.deviceType}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  {getFunctionIcon()}
                  <div>
                    <p className="text-sm text-gray-600">功能類型</p>
                    <p className="font-medium uppercase">{software.functionType}</p>
                  </div>
                </div>

                {software.description && (
                  <div>
                    <p className="text-sm text-gray-600 mb-1">描述</p>
                    <p className="text-gray-900">{software.description}</p>
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">檔案資訊</h3>
              
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-600">原始檔案名稱</p>
                  <p className="font-medium font-mono text-sm">{software.originalFilename}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-600">檔案大小</p>
                  <p className="font-medium">{formatFileSize(software.fileSize)}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-600">CRC校驗和</p>
                  <p className="font-medium font-mono text-sm">{software.checksum}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-600">分類</p>
                  <p className="font-medium capitalize">{software.category}</p>
                </div>

                {software.tags && software.tags.length > 0 && (
                  <div>
                    <p className="text-sm text-gray-600 mb-1">標籤</p>
                    <div className="flex flex-wrap gap-1">
                      {software.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 時間和使用統計 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">時間資訊</h3>
              
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Calendar className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">上傳時間</p>
                    <p className="font-medium">{formatDate(software.uploadDate)}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Calendar className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">最後修改</p>
                    <p className="font-medium">{formatDate(software.lastModified)}</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">使用統計</h3>
              
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-600">下載次數</p>
                  <p className="font-medium text-lg">{software.downloadCount}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-600">部署次數</p>
                  <p className="font-medium text-lg">{software.deploymentCount}</p>
                </div>
              </div>
            </div>
          </div>

          {/* 版本歷史 */}
          {software.versionHistory && software.versionHistory.length > 0 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">版本歷史</h3>
              
              <div className="space-y-3">
                {software.versionHistory.map((version, index) => (
                  <div key={index} className="p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">版本 {version.version}</span>
                      <span className="text-sm text-gray-600">{formatDate(version.uploadDate)}</span>
                    </div>
                    {version.changes && (
                      <p className="text-sm text-gray-700">{version.changes}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 底部操作區 */}
        <div className="flex justify-end gap-3 p-6 border-t bg-gray-50">
          <Button
            onClick={onClose}
            variant="outline"
          >
            關閉
          </Button>
        </div>
      </div>
    </div>
  );
}
