import React, { useState, useRef } from 'react';
import { X, Upload, AlertCircle, CheckCircle, FileText, HardDrive } from 'lucide-react';
import { Button } from '../ui/button';
import { uploadSoftware, validateBinFile, ValidationReport } from '../../utils/api/softwareApi';

interface SoftwareUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function SoftwareUploadModal({ isOpen, onClose, onSuccess }: SoftwareUploadModalProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [validationReport, setValidationReport] = useState<ValidationReport | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: 'general',
    tags: [] as string[]
  });
  const [uploading, setUploading] = useState(false);
  const [validating, setValidating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  if (!isOpen) return null;

  // 處理檔案選擇
  const handleFileSelect = async (file: File) => {
    setSelectedFile(file);
    setValidationReport(null);
    setError(null);
    
    // 自動填入檔案名稱
    if (!formData.name) {
      setFormData(prev => ({
        ...prev,
        name: file.name.replace('.bin', '')
      }));
    }

    // 驗證檔案
    try {
      setValidating(true);
      const report = await validateBinFile(file);
      setValidationReport(report);
    } catch (err) {
      setError(err instanceof Error ? err.message : '檔案驗證失敗');
    } finally {
      setValidating(false);
    }
  };

  // 處理拖拽
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    const binFile = files.find(file => file.name.toLowerCase().endsWith('.bin'));
    
    if (binFile) {
      handleFileSelect(binFile);
    } else {
      setError('請選擇.bin檔案');
    }
  };

  // 處理檔案輸入
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  // 處理表單提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedFile) {
      setError('請選擇要上傳的檔案');
      return;
    }

    if (!validationReport || validationReport.status !== 'valid') {
      setError('檔案驗證失敗，無法上傳');
      return;
    }

    if (!formData.name.trim()) {
      setError('請輸入軟體名稱');
      return;
    }

    try {
      setUploading(true);
      setError(null);
      
      await uploadSoftware(selectedFile, formData);
      onSuccess();
    } catch (err) {
      setError(err instanceof Error ? err.message : '上傳失敗');
    } finally {
      setUploading(false);
    }
  };

  // 重置表單
  const resetForm = () => {
    setSelectedFile(null);
    setValidationReport(null);
    setFormData({
      name: '',
      description: '',
      category: 'general',
      tags: []
    });
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 處理關閉
  const handleClose = () => {
    resetForm();
    onClose();
  };

  // 獲取驗證狀態圖標
  const getValidationIcon = () => {
    if (validating) {
      return <div className="animate-spin w-5 h-5 border-2 border-orange-500 border-t-transparent rounded-full" />;
    }
    
    if (!validationReport) return null;
    
    switch (validationReport.status) {
      case 'valid':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
      case 'unsupported':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* 標題列 */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">上傳軟體</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* 檔案上傳區域 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              選擇bin檔案 *
            </label>
            <div
              className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                dragOver
                  ? 'border-orange-500 bg-orange-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              {selectedFile ? (
                <div className="space-y-2">
                  <FileText className="w-12 h-12 text-orange-500 mx-auto" />
                  <p className="text-sm font-medium text-gray-900">{selectedFile.name}</p>
                  <p className="text-xs text-gray-500">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                  <div className="flex items-center justify-center gap-2">
                    {getValidationIcon()}
                    {validationReport && (
                      <span className={`text-sm ${
                        validationReport.status === 'valid' ? 'text-green-600' :
                        validationReport.status === 'error' ? 'text-red-600' :
                        'text-yellow-600'
                      }`}>
                        {validationReport.message}
                      </span>
                    )}
                  </div>
                  <button
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                    className="text-sm text-orange-600 hover:text-orange-700"
                  >
                    選擇其他檔案
                  </button>
                </div>
              ) : (
                <div className="space-y-2">
                  <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                  <p className="text-sm text-gray-600">
                    拖拽bin檔案到此處，或
                    <button
                      type="button"
                      onClick={() => fileInputRef.current?.click()}
                      className="text-orange-600 hover:text-orange-700 ml-1"
                    >
                      點擊選擇檔案
                    </button>
                  </p>
                  <p className="text-xs text-gray-500">支援最大100MB的.bin檔案</p>
                </div>
              )}
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept=".bin"
              onChange={handleFileInputChange}
              className="hidden"
            />
          </div>

          {/* 驗證結果詳細資訊 */}
          {validationReport && validationReport.details && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-900 mb-3">檔案資訊</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">設備類型:</span>
                  <span className="ml-2 font-medium">{validationReport.details.deviceType}</span>
                </div>
                <div>
                  <span className="text-gray-600">功能類型:</span>
                  <span className="ml-2 font-medium">{validationReport.details.functionType}</span>
                </div>
                <div>
                  <span className="text-gray-600">版本:</span>
                  <span className="ml-2 font-medium">{validationReport.details.version}</span>
                </div>
                <div>
                  <span className="text-gray-600">韌體大小:</span>
                  <span className="ml-2 font-medium">
                    {(validationReport.details.binSize / 1024).toFixed(1)} KB
                  </span>
                </div>
                <div className="col-span-2">
                  <span className="text-gray-600">CRC校驗:</span>
                  <span className="ml-2 font-mono text-xs">{validationReport.details.checksum}</span>
                </div>
              </div>
            </div>
          )}

          {/* 軟體資訊表單 */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                軟體名稱 *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="輸入軟體名稱"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                軟體描述
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="輸入軟體描述"
                rows={3}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                分類
              </label>
              <select
                value={formData.category}
                onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              >
                <option value="general">一般</option>
                <option value="firmware">韌體</option>
                <option value="driver">驅動程式</option>
                <option value="utility">工具程式</option>
                <option value="beta">測試版本</option>
              </select>
            </div>
          </div>

          {/* 錯誤提示 */}
          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg flex items-center">
              <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
              <span className="text-red-700">{error}</span>
            </div>
          )}

          {/* 操作按鈕 */}
          <div className="flex gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={uploading}
              className="flex-1"
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={uploading || !selectedFile || !validationReport || validationReport.status !== 'valid'}
              className="flex-1 bg-orange-500 hover:bg-orange-600 text-white"
            >
              {uploading ? (
                <>
                  <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                  上傳中...
                </>
              ) : (
                <>
                  <Upload className="w-4 h-4 mr-2" />
                  上傳軟體
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
