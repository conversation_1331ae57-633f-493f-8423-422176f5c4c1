import React, { useState, useEffect, useRef } from 'react';
import { TemplateElement } from '../../../types';
import { ControlHandle } from './ShapeElement';
import { constrainElementToCanvas } from '../canvasUtils';
import { BarChart3 } from 'lucide-react';
import { generateBarcodeForEditor, getBarcodeTypeDisplay, getSampleValueForBarcodeType } from '../../../utils/codeGenerators/barcodeGenerator';

interface BarcodeRendererProps {
  element: TemplateElement;
  isSelected: boolean;
  onSelect: (id: string, e?: React.MouseEvent) => void;
  onUpdate: (id: string, updates: Partial<TemplateElement>) => void;
  zoom?: number;
  setSelectedTool?: (tool: string | null) => void;
  selectedElementIds?: string[];
  moveSelectedElements?: (dx: number, dy: number) => void;
  isMultiMoving?: boolean;
}

export const BarcodeRenderer: React.FC<BarcodeRendererProps> = ({
  element,
  isSelected,
  onSelect,
  onUpdate,
  zoom = 100,
  setSelectedTool,
  selectedElementIds = [],
  moveSelectedElements,
  isMultiMoving = false
}) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startDragPosition, setStartDragPosition] = useState({ x: 0, y: 0 });
  const [isResizing, setIsResizing] = useState(false);
  const [activeHandle, setActiveHandle] = useState<ControlHandle | null>(null);
  const [isRotating, setIsRotating] = useState(false);
  const [rotationStartAngle, setRotationStartAngle] = useState(0);
  const [barcodeImage, setBarcodeImage] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  // 檢查是否為多選狀態
  const isMultiSelected = selectedElementIds.length > 1 && selectedElementIds.includes(element.id);

  // 檢查是否綁定了資料欄位
  const hasBoundDataField = !!element.dataFieldId || !!element.dataBinding?.fieldId;

  // 獲取內容預覽
  const getContentPreview = () => {
    if (element.dataBinding?.fieldId) {
      // 如果有資料綁定，使用設定的內容或範例值
      return element.codeContent || getSampleValueForBarcodeType(element.barcodeType || 'code128');
    }
    return element.codeContent || getSampleValueForBarcodeType(element.barcodeType || 'code128');
  };

  // 生成條碼圖片
  const generateBarcodeImage = async () => {
    if (isGenerating) return;

    setIsGenerating(true);
    try {
      const dataUrl = await generateBarcodeForEditor(element);
      setBarcodeImage(dataUrl);
    } catch (error) {
      console.error('條碼生成失敗:', error);
      setBarcodeImage(null);
    } finally {
      setIsGenerating(false);
    }
  };

  // 當元件屬性變更時重新生成條碼
  useEffect(() => {
    generateBarcodeImage();
  }, [
    element.codeContent,
    element.barcodeType,
    element.quietZone,
    element.lineColor,
    element.fillColor,
    element.width,
    element.height,
    element.dataBinding,
    element.showText
  ]);

  // 處理滑鼠按下事件 - 開始拖曳
  const handleMouseDown = (e: React.MouseEvent) => {
    if (!elementRef.current || isResizing || isRotating) return;

    e.stopPropagation();
    e.preventDefault(); // 防止文字選取
    setIsDragging(true);
    setStartDragPosition({ x: e.clientX, y: e.clientY });

    if (!isSelected) {
      onSelect(element.id);
    }
  };

  // 處理控制點滑鼠按下事件 - 開始調整大小或旋轉
  const handleControlPointMouseDown = (handle: ControlHandle, e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault(); // 防止文字選取

    if (handle === ControlHandle.Rotate) {
      setIsRotating(true);
      const rect = elementRef.current?.getBoundingClientRect();
      if (rect) {
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        const startAngle = Math.atan2(
          e.clientY - centerY,
          e.clientX - centerX
        ) * (180 / Math.PI);
        setRotationStartAngle(startAngle - (element.rotation || 0));
      }
    } else {
      setIsResizing(true);
      setActiveHandle(handle);
    }
  };

  // 處理滑鼠移動事件 - 拖曳、調整大小、旋轉
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging) {
        const deltaX = e.clientX - startDragPosition.x;
        const deltaY = e.clientY - startDragPosition.y;

        const scaledDeltaX = deltaX / (zoom / 100);
        const scaledDeltaY = deltaY / (zoom / 100);

        if (isMultiSelected && moveSelectedElements) {
          moveSelectedElements(scaledDeltaX, scaledDeltaY);
        } else {
          const newX = Math.round(element.x + scaledDeltaX);
          const newY = Math.round(element.y + scaledDeltaY);

          const canvasElement = elementRef.current?.closest('[data-canvas-width]');
          const canvasWidth = canvasElement ?
            parseInt(canvasElement.getAttribute('data-canvas-width') || '250', 10) : 250;
          const canvasHeight = canvasElement ?
            parseInt(canvasElement.getAttribute('data-canvas-height') || '122', 10) : 122;

          const constrainedUpdates = constrainElementToCanvas(
            { x: newX, y: newY, width: Math.round(element.width), height: Math.round(element.height) },
            canvasWidth,
            canvasHeight
          );

          onUpdate(element.id, constrainedUpdates);
        }

        setStartDragPosition({ x: e.clientX, y: e.clientY });
      } else if (isRotating) {
        const rect = elementRef.current?.getBoundingClientRect();
        if (rect) {
          const centerX = rect.left + rect.width / 2;
          const centerY = rect.top + rect.height / 2;
          const currentAngle = Math.atan2(
            e.clientY - centerY,
            e.clientX - centerX
          ) * (180 / Math.PI);
          const newRotation = Math.round(currentAngle - rotationStartAngle);
          onUpdate(element.id, { rotation: newRotation });
        }
      } else if (isResizing && activeHandle) {
        const rotation = element.rotation || 0;
        const rotationRad = (rotation * Math.PI) / 180;
        const cos = Math.cos(rotationRad);
        const sin = Math.sin(rotationRad);

        let scaledMovementX = e.movementX / (zoom / 100);
        let scaledMovementY = e.movementY / (zoom / 100);

        if (rotation !== 0) {
          const rotatedMovementX = scaledMovementX * cos + scaledMovementY * sin;
          const rotatedMovementY = -scaledMovementX * sin + scaledMovementY * cos;

          scaledMovementX = rotatedMovementX;
          scaledMovementY = rotatedMovementY;
        }

        let newX = Math.round(element.x);
        let newY = Math.round(element.y);
        let newWidth = Math.round(element.width);
        let newHeight = Math.round(element.height);

        // 條碼調整大小邏輯
        switch (activeHandle) {
          case ControlHandle.TopLeft:
            newWidth = Math.round(element.width - scaledMovementX);
            newHeight = Math.round(element.height - scaledMovementY);
            newX = Math.round(element.x + scaledMovementX);
            newY = Math.round(element.y + scaledMovementY);
            break;
          case ControlHandle.TopRight:
            newWidth = Math.round(element.width + scaledMovementX);
            newHeight = Math.round(element.height - scaledMovementY);
            newY = Math.round(element.y + scaledMovementY);
            break;
          case ControlHandle.BottomLeft:
            newWidth = Math.round(element.width - scaledMovementX);
            newHeight = Math.round(element.height + scaledMovementY);
            newX = Math.round(element.x + scaledMovementX);
            break;
          case ControlHandle.BottomRight:
            newWidth = Math.round(element.width + scaledMovementX);
            newHeight = Math.round(element.height + scaledMovementY);
            break;
        }

        if (newWidth < 50) newWidth = 50;  // 條碼需要更寬的最小寬度
        if (newHeight < 20) newHeight = 20;

        const canvasElement = elementRef.current?.closest('[data-canvas-width]');
        const canvasWidth = canvasElement ?
          parseInt(canvasElement.getAttribute('data-canvas-width') || '250', 10) : 250;
        const canvasHeight = canvasElement ?
          parseInt(canvasElement.getAttribute('data-canvas-height') || '122', 10) : 122;

        const constrainedElement = constrainElementToCanvas(
          { x: newX, y: newY, width: newWidth, height: newHeight },
          canvasWidth,
          canvasHeight
        );

        onUpdate(element.id, constrainedElement);
      }
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      setIsResizing(false);
      setIsRotating(false);
      setActiveHandle(null);
    };

    if (isDragging || isResizing || isRotating) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [
    isDragging,
    isResizing,
    isRotating,
    startDragPosition,
    activeHandle,
    rotationStartAngle,
    element,
    onUpdate,
    zoom,
    isMultiSelected,
    moveSelectedElements
  ]);



  // 條碼顏色不支援透明，如果是透明則使用黑色
  const foregroundColor = (element.lineColor === 'transparent' || !element.lineColor) ? '#000000' : element.lineColor;
  const backgroundColor = element.fillColor || '#FFFFFF';

  return (
    <div
      ref={elementRef}
      style={{
        position: 'absolute',
        left: element.x,
        top: element.y,
        width: element.width,
        height: element.height,
        cursor: isSelected ? 'move' : 'pointer',
        backgroundColor: backgroundColor,
        border: element.showBorder !== false
          ? `${element.lineWidth || 2}px solid ${element.borderColor || '#000000'}`
          : 'none',
        borderRadius: '4px',
        transform: element.rotation ? `rotate(${element.rotation}deg)` : undefined,
        transformOrigin: 'center center',
        outline: isSelected ? '1px dashed #3b82f6' : 'none',
        outlineOffset: '2px',
        zIndex: isSelected ? 10 : 1,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '8px',
        boxSizing: 'border-box',
        userSelect: 'none',
        WebkitUserSelect: 'none',
        MozUserSelect: 'none',
        msUserSelect: 'none'
      }}
      onClick={(e) => {
        e.stopPropagation();
        if (isMultiMoving) return;

        if (e.shiftKey && selectedElementIds.length > 0) {
          onSelect(element.id, e);
        } else {
          onSelect(element.id, e);
          if (setSelectedTool) {
            setSelectedTool(null);
          }
        }
      }}
      onMouseDown={handleMouseDown}
      onSelectStart={(e) => e.preventDefault()}
      onDragStart={(e) => e.preventDefault()}
      data-element-id={element.id}
      data-element-type="barcode"
      data-has-binding={hasBoundDataField ? 'true' : 'false'}
      data-field-id={element.dataBinding?.fieldId || element.dataFieldId || ''}
      data-store-id={element.dataBinding?.selectedStoreId || ''}
      data-index={element.dataBinding?.dataIndex !== undefined ? String(element.dataBinding.dataIndex) : '0'}
      data-item-uid={(element as any).storeItemUid || ''}
      data-debug-info={`dataIndex:${element.dataBinding?.dataIndex},uid:${(element as any).storeItemUid || 'none'}`}
      data-original-content={element.codeContent || 'Barcode'}
    >
      {/* 綁定狀態的視覺提示層 */}
      {hasBoundDataField && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            border: '1px dashed rgba(59, 130, 246, 0.5)',
            borderRadius: '2px',
            pointerEvents: 'none',
            zIndex: 1
          }}
        />
      )}

      {/* 如果有生成的條碼圖片，顯示真實的條碼 */}
      {barcodeImage ? (
        <img
          src={barcodeImage}
          alt="條碼"
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'contain',
            pointerEvents: 'none'
          }}
          draggable={false}
        />
      ) : isGenerating ? (
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            width: '100%',
            height: '100%',
            color: foregroundColor,
            pointerEvents: 'none',
            userSelect: 'none'
          }}
        >
          <BarChart3
            size={Math.min(element.width, element.height) * 0.3}
            color={foregroundColor}
            strokeWidth={2}
            style={{ pointerEvents: 'none' }}
          />
          <div style={{
            fontSize: '8px',
            marginTop: '4px',
            pointerEvents: 'none',
            userSelect: 'none'
          }}>生成中...</div>
        </div>
      ) : (
        <>
          {/* 沒有圖片時顯示佔位符 */}
          <BarChart3
            size={Math.min(element.width, element.height) * 0.4}
            color={foregroundColor}
            strokeWidth={2}
            style={{ pointerEvents: 'none' }}
          />

          {/* 條碼類型標籤 */}
          <div
            style={{
              fontSize: '10px',
              color: foregroundColor,
              marginTop: '4px',
              textAlign: 'center',
              fontWeight: 'bold',
              pointerEvents: 'none',
              userSelect: 'none'
            }}
          >
            {getBarcodeTypeDisplay(element.barcodeType)}
          </div>

          {/* 內容預覽 */}
          <div
            style={{
              fontSize: '8px',
              color: foregroundColor,
              marginTop: '2px',
              textAlign: 'center',
              opacity: 0.7,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              maxWidth: '100%',
              pointerEvents: 'none',
              userSelect: 'none'
            }}
          >
            {getContentPreview()}
          </div>

          {/* 設定摘要 */}
          <div
            style={{
              fontSize: '7px',
              color: foregroundColor,
              marginTop: '2px',
              opacity: 0.5,
              textAlign: 'center',
              pointerEvents: 'none',
              userSelect: 'none'
            }}
          >
            高度: {element.height}px
          </div>
        </>
      )}

      {/* 綁定指示器 */}
      {hasBoundDataField && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            right: 0,
            fontSize: isSelected ? '2px' : '4px',
            color: 'white',
            backgroundColor: isSelected ? 'rgba(59, 130, 246, 0.3)' : 'rgba(59, 130, 246, 0.7)',
            padding: '2px 4px',
            borderRadius: '0 0 0 4px',
            pointerEvents: 'none',
            userSelect: 'none',
            zIndex: 5
          }}
        >
          已綁定
        </div>
      )}

      {/* 只在單選狀態下顯示控制點，多選狀態下不顯示 */}
      {isSelected && !isMultiSelected && (
        <>
          {/* 角落控制點 */}
          <div
            style={{
              position: 'absolute',
              left: -4,
              top: -4,
              width: 8,
              height: 8,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'nwse-resize',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.TopLeft, e)}
          />
          <div
            style={{
              position: 'absolute',
              right: -4,
              top: -4,
              width: 8,
              height: 8,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'nesw-resize',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.TopRight, e)}
          />
          <div
            style={{
              position: 'absolute',
              left: -4,
              bottom: -4,
              width: 8,
              height: 8,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'nesw-resize',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.BottomLeft, e)}
          />
          <div
            style={{
              position: 'absolute',
              right: -4,
              bottom: -4,
              width: 8,
              height: 8,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'nwse-resize',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.BottomRight, e)}
          />

          {/* 旋轉控制點 */}
          <div
            style={{
              position: 'absolute',
              left: '50%',
              top: -25,
              width: 8,
              height: 8,
              marginLeft: -4,
              backgroundColor: 'transparent',
              border: '0.5px dashed #3b82f6',
              borderRadius: '50%',
              cursor: 'url("data:image/svg+xml,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22%3E%3Cpath fill=%22%23000000%22 d=%22M7.11 8.53L5.7 7.11C4.8 8.27 4.24 9.61 4.07 11h2.02c.14-.87.49-1.72 1.02-2.47zM6.09 13H4.07c.17 1.39.72 2.73 1.62 3.89l1.41-1.42c-.52-.75-.87-1.59-1.01-2.47zm1.01 5.32c1.16.9 2.51 1.44 3.9 1.61V17.9c-.87-.15-1.71-.49-2.46-1.03L7.1 18.32zM13 4.07V1L8.45 5.55 13 10V6.09c2.84.48 5 2.94 5 5.91s-2.16 5.43-5 5.91v2.02c3.95-.49 7-3.85 7-7.93s-3.05-7.44-7-7.93z%22/%3E%3C/svg%3E") 12 12, auto',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.Rotate, e)}
          />

          {/* 旋轉控制點連接線 */}
          <div
            style={{
              position: 'absolute',
              left: '50%',
              top: -15,
              width: 1,
              height: 15,
              marginLeft: -0.5,
              backgroundColor: '#3b82f6',
              zIndex: 99
            }}
          />
        </>
      )}
    </div>
  );
};
