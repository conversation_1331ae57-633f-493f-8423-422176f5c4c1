#!/usr/bin/env python3
"""
Bin檔案格式化工具測試腳本
"""

import os
import tempfile
import struct
import zlib
from bin_formatter import BinFormatter

def create_test_bin_file(content: bytes = b"test binary data") -> str:
    """創建測試用的bin檔案"""
    with tempfile.NamedTemporaryFile(suffix='.bin', delete=False) as f:
        f.write(content)
        return f.name

def test_basic_functionality():
    """測試基本功能"""
    print("=== 測試基本功能 ===")
    
    formatter = BinFormatter()
    
    # 創建測試檔案
    test_data = b"Hello, this is test firmware data!"
    test_bin_path = create_test_bin_file(test_data)
    
    try:
        # 測試格式化
        output_path = formatter.format_bin_file(
            bin_path=test_bin_path,
            device_type="gateway",
            function_type="wifi",
            version="1.2.3.4"
        )
        
        print(f"✅ 成功生成檔案: {os.path.basename(output_path)}")
        
        # 驗證輸出檔案格式
        with open(output_path, 'rb') as f:
            data = f.read()
        
        # 解析檔案格式
        device_type = struct.unpack('<H', data[0:2])[0]
        function_type = struct.unpack('<H', data[2:4])[0]
        version = list(data[4:8])
        bin_content = data[8:-4]
        checksum = struct.unpack('<I', data[-4:])[0]
        
        print(f"  設備類型: {device_type} (應為0)")
        print(f"  功能類型: {function_type} (應為0)")
        print(f"  版本信息: {'.'.join(map(str, version))} (應為1.2.3.4)")
        print(f"  bin內容長度: {len(bin_content)} bytes")
        print(f"  校驗和: 0x{checksum:08X}")
        
        # 驗證校驗和
        expected_checksum = zlib.crc32(test_data) & 0xffffffff
        if checksum == expected_checksum:
            print("  ✅ 校驗和正確")
        else:
            print(f"  ❌ 校驗和錯誤，期望: 0x{expected_checksum:08X}")
        
        # 驗證bin內容
        if bin_content == test_data:
            print("  ✅ bin內容正確")
        else:
            print("  ❌ bin內容不匹配")
            
    finally:
        # 清理測試檔案
        if os.path.exists(test_bin_path):
            os.unlink(test_bin_path)

def test_validation():
    """測試驗證功能"""
    print("\n=== 測試驗證功能 ===")
    
    formatter = BinFormatter()
    test_bin_path = create_test_bin_file()
    
    try:
        # 測試不支援的設備類型
        try:
            formatter.format_bin_file(test_bin_path, "invalid_device", "wifi", "1.0.0.0")
            print("❌ 應該拒絕無效設備類型")
        except ValueError as e:
            print(f"✅ 正確拒絕無效設備類型: {e}")
        
        # 測試不支援的功能類型
        try:
            formatter.format_bin_file(test_bin_path, "gateway", "invalid_function", "1.0.0.0")
            print("❌ 應該拒絕無效功能類型")
        except ValueError as e:
            print(f"✅ 正確拒絕無效功能類型: {e}")
        
        # 測試設備功能不匹配
        try:
            formatter.format_bin_file(test_bin_path, "epd", "wifi", "1.0.0.0")
            print("❌ 應該拒絕EPD使用WiFi")
        except ValueError as e:
            print(f"✅ 正確拒絕EPD使用WiFi: {e}")
        
        # 測試無效版本格式
        invalid_versions = ["1.0.0", "1.0.0.0.0", "a.b.c.d", "1.0"]
        for version in invalid_versions:
            try:
                formatter.format_bin_file(test_bin_path, "gateway", "wifi", version)
                print(f"❌ 應該拒絕無效版本格式: {version}")
            except ValueError as e:
                print(f"✅ 正確拒絕無效版本格式 {version}: {e}")
        
        # 測試不存在的檔案
        try:
            formatter.format_bin_file("nonexistent.bin", "gateway", "wifi", "1.0.0.0")
            print("❌ 應該拒絕不存在的檔案")
        except FileNotFoundError as e:
            print(f"✅ 正確拒絕不存在的檔案: {e}")
            
    finally:
        if os.path.exists(test_bin_path):
            os.unlink(test_bin_path)

def test_all_combinations():
    """測試所有有效的設備和功能組合"""
    print("\n=== 測試所有有效組合 ===")
    
    formatter = BinFormatter()
    test_bin_path = create_test_bin_file()
    
    valid_combinations = [
        ("gateway", "wifi"),
        ("gateway", "ble"),
        ("epd", "ble")
    ]
    
    try:
        for device_type, function_type in valid_combinations:
            output_path = formatter.format_bin_file(
                test_bin_path, device_type, function_type, "2.1.0.5"
            )
            print(f"✅ {device_type} + {function_type}: {os.path.basename(output_path)}")
            
    finally:
        if os.path.exists(test_bin_path):
            os.unlink(test_bin_path)

def main():
    """執行所有測試"""
    print("開始測試 Bin檔案格式化工具...")
    
    test_basic_functionality()
    test_validation()
    test_all_combinations()
    
    print("\n=== 測試完成 ===")
    print("請檢查 output/ 目錄中的生成檔案")

if __name__ == "__main__":
    main()
