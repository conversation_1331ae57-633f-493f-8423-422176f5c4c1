// server/routes/softwareApi.js
const express = require('express');
const multer = require('multer');
const { ObjectId } = require('mongodb');
const { authenticate, checkPermission } = require('../middleware/auth');
const Software = require('../models/Software');
const BinFileParser = require('../services/BinFileParser');

const router = express.Router();

// 配置multer用於檔案上傳
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB限制
  },
  fileFilter: (req, file, cb) => {
    // 只允許.bin檔案
    if (file.originalname.toLowerCase().endsWith('.bin')) {
      cb(null, true);
    } else {
      cb(new Error('只允許上傳.bin檔案'), false);
    }
  }
});

// 數據庫連接函數引用
let connectDBFunction;

// 初始化數據庫連接
function initDB(connectDB) {
  connectDBFunction = connectDB;
  return connectDBFunction;
}

// 中間件處理檔案名亂碼問題
const fixFileNameEncoding = (req, res, next) => {
  if (req.file) {
    req.file.originalname = Buffer.from(req.file.originalname, "latin1").toString("utf-8");
  }
  next();
};

/**
 * 獲取軟體列表
 * GET /api/software
 */
router.get('/software', authenticate, checkPermission('software:read'), async (req, res) => {
  try {
    const {
      deviceType,
      functionType,
      status,
      page = 1,
      limit = 20,
      search,
      sortBy = 'uploadDate',
      sortOrder = 'desc'
    } = req.query;

    // 構建過濾條件
    const filters = {};
    if (deviceType && deviceType !== 'all') filters.deviceType = deviceType;
    if (functionType && functionType !== 'all') filters.functionType = functionType;
    if (status && status !== 'all') filters.status = status;

    // 構建查詢選項
    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      search,
      sortBy,
      sortOrder: sortOrder === 'desc' ? -1 : 1
    };

    const { db } = req;
    const result = await Software.findAll(db, filters, options);

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('獲取軟體列表錯誤:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'FETCH_ERROR',
        message: '獲取軟體列表失敗',
        details: error.message
      }
    });
  }
});

/**
 * 獲取軟體詳細資訊
 * GET /api/software/:id
 */
router.get('/software/:id', authenticate, checkPermission('software:read'), async (req, res) => {
  try {
    const { id } = req.params;

    if (!ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_ID',
          message: '無效的軟體ID'
        }
      });
    }

    const { db } = req;
    const software = await Software.findById(db, id);

    if (!software) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SOFTWARE_NOT_FOUND',
          message: '軟體不存在'
        }
      });
    }

    res.json({
      success: true,
      data: software
    });
  } catch (error) {
    console.error('獲取軟體詳細資訊錯誤:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'FETCH_ERROR',
        message: '獲取軟體詳細資訊失敗',
        details: error.message
      }
    });
  }
});

/**
 * 上傳軟體
 * POST /api/software/upload
 */
router.post('/software/upload',
  authenticate,
  checkPermission('software:create'),
  upload.single('file'),
  fixFileNameEncoding,
  async (req, res) => {
    try {
      const { name, description, category, tags } = req.body;
      const file = req.file;

      if (!file) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'NO_FILE',
            message: '請選擇要上傳的檔案'
          }
        });
      }

      // 解析bin檔案
      const parser = new BinFileParser();
      let binInfo;
      
      try {
        binInfo = parser.parseBinFile(file.buffer);
      } catch (parseError) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'PARSE_ERROR',
            message: parseError.message
          }
        });
      }

      // 驗證設備功能支援
      if (!parser.validateDeviceFunctionSupport(binInfo.deviceType, binInfo.functionType)) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'UNSUPPORTED_COMBINATION',
            message: `${binInfo.deviceType} 不支援 ${binInfo.functionType} 功能`
          }
        });
      }

      // 儲存檔案到GridFS
      const { gridFSBucket } = await connectDBFunction();

      // 儲存原始檔案
      const originalFileId = await saveToGridFS(gridFSBucket, file.buffer, file.originalname, {
        type: 'original_firmware',
        deviceType: binInfo.deviceType,
        functionType: binInfo.functionType,
        version: binInfo.version,
        uploadedBy: req.user._id.toString(),
        uploadDate: new Date()
      });

      // 儲存純bin內容
      const pureBinFilename = `${name || file.originalname.replace('.bin', '')}_pure.bin`;
      const pureBinId = await saveToGridFS(gridFSBucket, binInfo.binData, pureBinFilename, {
        type: 'pure_firmware',
        deviceType: binInfo.deviceType,
        functionType: binInfo.functionType,
        version: binInfo.version,
        uploadedBy: req.user._id.toString(),
        uploadDate: new Date()
      });

      // 處理標籤
      let parsedTags = [];
      if (tags) {
        try {
          parsedTags = typeof tags === 'string' ? JSON.parse(tags) : tags;
        } catch (e) {
          parsedTags = typeof tags === 'string' ? tags.split(',').map(t => t.trim()) : [];
        }
      }

      // 儲存軟體資訊
      const { db } = req;
      const software = await Software.createSoftware(db, {
        name: name || file.originalname.replace('.bin', ''),
        description: description || '',
        version: binInfo.version,
        deviceType: binInfo.deviceType,
        functionType: binInfo.functionType,
        originalFilename: file.originalname,
        fileSize: file.size,
        checksum: binInfo.checksum,
        binFileId: originalFileId,
        extractedBinId: pureBinId,
        category: category || 'general',
        tags: parsedTags,
        uploadedBy: req.user._id
      });

      res.status(201).json({
        success: true,
        data: {
          id: software._id,
          message: '軟體上傳成功',
          software: {
            name: software.name,
            version: software.version,
            deviceType: software.deviceType,
            functionType: software.functionType,
            fileSize: software.fileSize
          }
        }
      });

    } catch (error) {
      console.error('軟體上傳錯誤:', error);

      let errorCode = 'UPLOAD_ERROR';
      if (error.message.includes('CRC')) errorCode = 'CRC_VALIDATION_FAILED';
      if (error.message.includes('已存在')) errorCode = 'DUPLICATE_SOFTWARE';
      if (error.message.includes('不支援')) errorCode = 'UNSUPPORTED_DEVICE_TYPE';

      res.status(400).json({
        success: false,
        error: {
          code: errorCode,
          message: error.message
        }
      });
    }
  }
);

/**
 * 更新軟體狀態
 * PUT /api/software/:id/status
 */
router.put('/software/:id/status',
  authenticate,
  checkPermission('software:update'),
  async (req, res) => {
    try {
      const { id } = req.params;
      const { isEnabled, status } = req.body;

      if (!ObjectId.isValid(id)) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_ID',
            message: '無效的軟體ID'
          }
        });
      }

      const { db } = req;

      // 檢查軟體是否存在
      const software = await Software.findById(db, id);
      if (!software) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'SOFTWARE_NOT_FOUND',
            message: '軟體不存在'
          }
        });
      }

      // 構建更新數據
      const updateData = {};
      if (typeof isEnabled === 'boolean') updateData.isEnabled = isEnabled;
      if (status) updateData.status = status;

      const success = await Software.updateStatus(db, id, updateData, req.user._id);

      if (success) {
        res.json({
          success: true,
          message: '軟體狀態更新成功'
        });
      } else {
        res.status(400).json({
          success: false,
          error: {
            code: 'UPDATE_FAILED',
            message: '軟體狀態更新失敗'
          }
        });
      }
    } catch (error) {
      console.error('更新軟體狀態錯誤:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'UPDATE_ERROR',
          message: '更新軟體狀態失敗',
          details: error.message
        }
      });
    }
  }
);

// 輔助函數：儲存到GridFS
async function saveToGridFS(gridFSBucket, buffer, filename, metadata = {}) {
  return new Promise((resolve, reject) => {
    const uploadStream = gridFSBucket.openUploadStream(filename, { metadata });

    uploadStream.write(buffer);
    uploadStream.end();

    uploadStream.on('finish', () => {
      resolve(uploadStream.id);
    });

    uploadStream.on('error', (error) => {
      reject(error);
    });
  });
}

/**
 * 下載軟體檔案
 * GET /api/software/:id/download
 */
router.get('/software/:id/download',
  authenticate,
  checkPermission('software:read'),
  async (req, res) => {
    try {
      const { id } = req.params;
      const { type = 'original' } = req.query; // original 或 pure

      if (!ObjectId.isValid(id)) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_ID',
            message: '無效的軟體ID'
          }
        });
      }

      const { db } = req;
      const { gridFSBucket } = await connectDBFunction();
      const software = await Software.findById(db, id);

      if (!software) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'SOFTWARE_NOT_FOUND',
            message: '軟體不存在'
          }
        });
      }

      // 選擇要下載的檔案ID
      const fileId = type === 'pure' ? software.extractedBinId : software.binFileId;

      if (!fileId) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'FILE_NOT_FOUND',
            message: '檔案不存在'
          }
        });
      }

      // 增加下載次數
      await Software.incrementDownloadCount(db, id);

      // 設置回應標頭
      const filename = type === 'pure'
        ? `${software.name}_v${software.version}_pure.bin`
        : software.originalFilename;

      res.set({
        'Content-Type': 'application/octet-stream',
        'Content-Disposition': `attachment; filename="${encodeURIComponent(filename)}"`
      });

      // 創建下載流
      const downloadStream = gridFSBucket.openDownloadStream(fileId);

      downloadStream.on('error', (error) => {
        console.error('下載檔案錯誤:', error);
        if (!res.headersSent) {
          res.status(500).json({
            success: false,
            error: {
              code: 'DOWNLOAD_ERROR',
              message: '下載檔案失敗'
            }
          });
        }
      });

      downloadStream.pipe(res);

    } catch (error) {
      console.error('下載軟體檔案錯誤:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'DOWNLOAD_ERROR',
          message: '下載軟體檔案失敗',
          details: error.message
        }
      });
    }
  }
);

/**
 * 刪除軟體
 * DELETE /api/software/:id
 */
router.delete('/software/:id',
  authenticate,
  checkPermission('software:delete'),
  async (req, res) => {
    try {
      const { id } = req.params;

      if (!ObjectId.isValid(id)) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_ID',
            message: '無效的軟體ID'
          }
        });
      }

      const { db } = req;
      const { gridFSBucket } = await connectDBFunction();

      // 獲取軟體資訊
      const software = await Software.findById(db, id);
      if (!software) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'SOFTWARE_NOT_FOUND',
            message: '軟體不存在'
          }
        });
      }

      // 刪除GridFS中的檔案
      try {
        if (software.binFileId) {
          await gridFSBucket.delete(software.binFileId);
        }
        if (software.extractedBinId) {
          await gridFSBucket.delete(software.extractedBinId);
        }
      } catch (gridError) {
        console.warn('刪除GridFS檔案時發生錯誤:', gridError);
        // 繼續執行，不中斷刪除流程
      }

      // 刪除軟體記錄
      const success = await Software.deleteSoftware(db, id);

      if (success) {
        res.json({
          success: true,
          message: '軟體刪除成功'
        });
      } else {
        res.status(400).json({
          success: false,
          error: {
            code: 'DELETE_FAILED',
            message: '軟體刪除失敗'
          }
        });
      }
    } catch (error) {
      console.error('刪除軟體錯誤:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'DELETE_ERROR',
          message: '刪除軟體失敗',
          details: error.message
        }
      });
    }
  }
);

/**
 * 獲取啟用的軟體列表（供其他頁面選單使用）
 * GET /api/software/enabled
 */
router.get('/software/enabled',
  authenticate,
  checkPermission('software:read'),
  async (req, res) => {
    try {
      const { deviceType, functionType } = req.query;

      const filters = {};
      if (deviceType && deviceType !== 'all') filters.deviceType = deviceType;
      if (functionType && functionType !== 'all') filters.functionType = functionType;

      const { db } = req;
      const software = await Software.getEnabledSoftware(db, filters);

      res.json({
        success: true,
        data: software
      });
    } catch (error) {
      console.error('獲取啟用軟體列表錯誤:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'FETCH_ERROR',
          message: '獲取啟用軟體列表失敗',
          details: error.message
        }
      });
    }
  }
);

/**
 * 獲取軟體統計資訊
 * GET /api/software/statistics
 */
router.get('/software/statistics',
  authenticate,
  checkPermission('software:read'),
  async (req, res) => {
    try {
      const { db } = req;
      const statistics = await Software.getStatistics(db);

      res.json({
        success: true,
        data: statistics
      });
    } catch (error) {
      console.error('獲取軟體統計資訊錯誤:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'FETCH_ERROR',
          message: '獲取軟體統計資訊失敗',
          details: error.message
        }
      });
    }
  }
);

/**
 * 驗證bin檔案
 * POST /api/software/validate
 */
router.post('/software/validate',
  authenticate,
  checkPermission('software:read'),
  upload.single('file'),
  fixFileNameEncoding,
  async (req, res) => {
    try {
      const file = req.file;

      if (!file) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'NO_FILE',
            message: '請選擇要驗證的檔案'
          }
        });
      }

      const parser = new BinFileParser();
      const report = parser.generateSummaryReport(file.buffer, file.originalname);

      res.json({
        success: true,
        data: report
      });
    } catch (error) {
      console.error('驗證bin檔案錯誤:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '驗證bin檔案失敗',
          details: error.message
        }
      });
    }
  }
);

module.exports = { router, initDB };
